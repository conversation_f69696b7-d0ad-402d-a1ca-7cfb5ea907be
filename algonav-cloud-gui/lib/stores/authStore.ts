import { create } from 'zustand'
import { User } from '@supabase/supabase-js'

interface AuthState {
  user: User | null
  userRole: string
  isAdmin: boolean
  setUser: (user: User | null) => void
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  userRole: 'user',
  isAdmin: false,
  setUser: (user) => {
    const userRole = user?.app_metadata?.role || 'user';
    const isAdmin = userRole === 'admin';
    set({ user, userRole, isAdmin });
  }
}))
