import { User } from '@supabase/supabase-js';

/**
 * Check if a user has admin role
 */
export function isAdmin(user: User | null): boolean {
  if (!user) return false;
  return user.app_metadata?.role === 'admin';
}

/**
 * Get user role from user object
 */
export function getUserRole(user: User | null): string {
  if (!user) return 'user';
  return user.app_metadata?.role || 'user';
}

/**
 * Check if user is support staff (admin or assignee)
 */
export function isSupportStaff(user: User | null, ticket: any): boolean {
  if (!user || !ticket) return false;
  
  // Check if user is admin
  if (isAdmin(user)) return true;
  
  // Check if user is assignee
  return ticket.assignee?.id === user.id;
}

/**
 * Check if user is customer (creator but not support staff)
 */
export function isCustomer(user: User | null, ticket: any): boolean {
  if (!user || !ticket) return false;
  
  // User is customer if they're the creator but not support staff
  return ticket.creator?.id === user.id && !isSupportStaff(user, ticket);
}

/**
 * Get display name for user in messages
 */
export function getMessageDisplayName(user: User | null): string {
  if (!user) return 'Unknown User';
  
  if (isAdmin(user)) {
    return 'Algonav Support';
  }
  
  return user.email || 'User';
}
