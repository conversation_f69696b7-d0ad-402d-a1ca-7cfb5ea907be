import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export interface AuthContext {
    userId: string;
    userRole: string;
    isAdmin: boolean;
}

type RouteHandler = (
    authContext: AuthContext,
    request: Request,
    context: { params: Record<string, string | string[]> }
) => Promise<NextResponse>;

export function withAuth(handler: RouteHandler) {
    return async function(
        request: Request,
        context: { params: Record<string, string | string[]> }
    ) {
        try {
            const supabase = createClient();
            const { data: { user } } = await supabase.auth.getUser();

            if (!user?.id) {
                return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }

            // Get user role from app_metadata
            const userRole = user.app_metadata?.role || 'user';
            const isAdmin = userRole === 'admin';

            const authContext: AuthContext = {
                userId: user.id,
                userRole,
                isAdmin
            };

            return handler(authContext, request, context);
        } catch (error) {
            return NextResponse.json(
                { error: 'Internal Server Error' },
                { status: 500 }
            );
        }
    }
}