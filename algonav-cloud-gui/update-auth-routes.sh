#!/bin/bash

# Script to update all API routes to use the new authContext parameter

# List of files to update (excluding already updated ones)
files=(
    "app/api/templates/[id]/route.ts"
    "app/api/jobs/[id]/route.ts"
    "app/api/datasets/[id]/route.ts"
    "app/api/templates/route.ts"
    "app/api/gui-components/route.ts"
    "app/api/tasks/[id]/files/[fileId]/route.ts"
    "app/api/categories/route.ts"
    "app/api/datasets/route.ts"
    "app/api/tasks/route.ts"
    "app/api/notifications/route.ts"
    "app/api/file/route.ts"
    "app/api/dataset-files/route.ts"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "Updating $file..."
        
        # Replace withAuth callback parameter from userId to authContext
        sed -i '' 's/withAuth(async (userId,/withAuth(async (authContext,/g' "$file"
        sed -i '' 's/withAuth(async (userId)/withAuth(async (authContext)/g' "$file"
        sed -i '' 's/withAuth(async (userId:/withAuth(async (authContext:/g' "$file"
        
        # Add userId extraction after authContext parameter
        sed -i '' '/withAuth(async (authContext/a\
    const { userId } = authContext;
' "$file"
        
        echo "Updated $file"
    else
        echo "File not found: $file"
    fi
done

echo "All files updated!"
