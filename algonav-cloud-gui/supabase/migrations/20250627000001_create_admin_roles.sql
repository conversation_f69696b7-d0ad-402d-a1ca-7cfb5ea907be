-- Migration: Create Admin Roles System
-- Adds support for user roles via app_metadata and extends RLS policies for admin access

-- Step 1: <PERSON>reate function to set user role in app_metadata
CREATE OR REPLACE FUNCTION public.set_user_role(user_id uuid, role text)
R<PERSON>URNS void AS $$
BEGIN
    -- Update the user's app_metadata with the role
    UPDATE auth.users 
    SET app_metadata = COALESCE(app_metadata, '{}'::jsonb) || jsonb_build_object('role', role)
    WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 2: Create function to check if user is admin
CREATE OR REPLACE FUNCTION public.is_admin(user_id uuid DEFAULT auth.uid())
RETURNS boolean AS $$
BEGIN
    RETURN COALESCE(
        (SELECT (app_metadata->>'role') = 'admin' FROM auth.users WHERE id = user_id),
        false
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 3: Create function to get user role
CREATE OR REPLACE FUNCTION public.get_user_role(user_id uuid DEFAULT auth.uid())
RETURNS text AS $$
BEGIN
    RETURN COALESCE(
        (SELECT app_metadata->>'role' FROM auth.users WHERE id = user_id),
        'user'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Update RLS policies for tickets to allow admin access

-- Drop existing policies that we need to modify
DROP POLICY IF EXISTS "tickets_creator_access" ON "public"."tickets";
DROP POLICY IF EXISTS "tickets_assignee_access" ON "public"."tickets";

-- Create new policies that include admin access
CREATE POLICY "tickets_creator_access" ON "public"."tickets"
    AS permissive FOR ALL TO public
    USING (auth.uid() = creator_id OR public.is_admin());

CREATE POLICY "tickets_assignee_access" ON "public"."tickets"
    AS permissive FOR ALL TO public
    USING (auth.uid() = assignee_id OR public.is_admin());

-- Step 5: Update RLS policies for ticket_targets to allow admin access
DROP POLICY IF EXISTS "ticket_targets_access" ON "public"."ticket_targets";

CREATE POLICY "ticket_targets_access" ON "public"."ticket_targets"
    AS permissive FOR ALL TO public
    USING (
        EXISTS (
            SELECT 1 FROM public.tickets
            WHERE tickets.id = ticket_targets.ticket_id
            AND (tickets.creator_id = auth.uid() OR tickets.assignee_id = auth.uid() OR public.is_admin())
        )
    );

-- Step 6: Update RLS policies for ticket_messages to allow admin access
DROP POLICY IF EXISTS "ticket_messages_access" ON "public"."ticket_messages";

CREATE POLICY "ticket_messages_access" ON "public"."ticket_messages"
    AS permissive FOR ALL TO public
    USING (
        EXISTS (
            SELECT 1 FROM public.tickets
            WHERE tickets.id = ticket_messages.ticket_id
            AND (tickets.creator_id = auth.uid() OR tickets.assignee_id = auth.uid() OR public.is_admin())
        )
    );

-- Step 7: Update RLS policies for ticket_status_history to allow admin access
DROP POLICY IF EXISTS "ticket_status_history_access" ON "public"."ticket_status_history";

CREATE POLICY "ticket_status_history_access" ON "public"."ticket_status_history"
    AS permissive FOR SELECT TO public
    USING (
        EXISTS (
            SELECT 1 FROM public.tickets
            WHERE tickets.id = ticket_status_history.ticket_id
            AND (tickets.creator_id = auth.uid() OR tickets.assignee_id = auth.uid() OR public.is_admin())
        )
    );

-- Update the insert policy for ticket_status_history
DROP POLICY IF EXISTS "ticket_status_history_insert" ON "public"."ticket_status_history";

CREATE POLICY "ticket_status_history_insert" ON "public"."ticket_status_history"
    AS permissive FOR INSERT TO public
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.tickets
        WHERE tickets.id = ticket_status_history.ticket_id
        AND (tickets.creator_id = auth.uid() OR tickets.assignee_id = auth.uid() OR public.is_admin())
    ));

-- Step 8: Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.set_user_role(uuid, text) TO service_role;
GRANT EXECUTE ON FUNCTION public.is_admin(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_role(uuid) TO authenticated;

-- Step 9: Add helpful comments
COMMENT ON FUNCTION public.set_user_role(uuid, text) IS 'Sets the role for a user in their app_metadata. Only callable by service_role.';
COMMENT ON FUNCTION public.is_admin(uuid) IS 'Checks if a user has admin role. Defaults to current authenticated user.';
COMMENT ON FUNCTION public.get_user_role(uuid) IS 'Gets the role for a user from their app_metadata. Defaults to "user" if no role is set.';
