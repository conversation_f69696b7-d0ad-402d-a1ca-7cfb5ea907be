import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";
import { TicketMessage, TicketStatusHistory } from '@/types/ticket';

export const GET = withAuth(async (authContext, request, { params }) => {
    const { userId } = authContext;
    const supabase = createClient();
    const { id } = params;

    if (!id) {
        return NextResponse.json({ error: 'Ticket ID is required' }, { status: 400 });
    }

    try {
        const { data: ticket, error } = await supabase
            .from('tickets')
            .select(`
                *,
                ticket_targets(
                    id,
                    target_type,
                    target_id
                ),
                ticket_messages(
                    id,
                    body,
                    created_at,
                    author_id
                ),
                ticket_status_history(
                    id,
                    old_status,
                    new_status,
                    changed_at,
                    changed_by
                )
            `)
            .eq('id', id)
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
            }
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        // Fetch related item names for ticket targets
        if (ticket.ticket_targets && ticket.ticket_targets.length > 0) {
            for (const target of ticket.ticket_targets) {
                if (target.target_type === 'job') {
                    const { data: job } = await supabase
                        .from('jobs')
                        .select('name')
                        .eq('id', target.target_id)
                        .eq('user_id', userId)
                        .single();
                    target.name = job?.name || `Job #${target.target_id}`;
                } else if (target.target_type === 'task') {
                    const { data: task } = await supabase
                        .from('tasks')
                        .select('name, job_id')
                        .eq('id', target.target_id)
                        .eq('user_id', userId)
                        .single();
                    target.name = task?.name || `Task #${target.target_id}`;
                    target.job_id = task?.job_id;
                } else if (target.target_type === 'dataset') {
                    const { data: dataset } = await supabase
                        .from('datasets')
                        .select('name')
                        .eq('id', target.target_id)
                        .eq('user_id', userId)
                        .single();
                    target.name = dataset?.name || `Dataset #${target.target_id}`;
                }
            }
        }

        // Fetch user information separately
        const userIds = new Set<string>();
        if (ticket.creator_id) userIds.add(ticket.creator_id);
        if (ticket.assignee_id) userIds.add(ticket.assignee_id);

        // Add user IDs from comments and status history
        if (ticket.ticket_messages) {
            ticket.ticket_messages.forEach((message: TicketMessage) => {
                if (message.author_id) userIds.add(message.author_id);
            });
        }

        if (ticket.ticket_status_history) {
            ticket.ticket_status_history.forEach((history: TicketStatusHistory) => {
                if (history.changed_by) userIds.add(history.changed_by);
            });
        }

        const { data: users, error: usersError } = await supabase.auth.admin.listUsers();

        if (!usersError && users) {
            const userMap = new Map();
            users.users.forEach(user => {
                userMap.set(user.id, { id: user.id, email: user.email });
            });

            // Add user information to ticket
            ticket.creator = userMap.get(ticket.creator_id) || null;
            ticket.assignee = userMap.get(ticket.assignee_id) || null;

            // Add user information to comments
            if (ticket.ticket_messages) {
                ticket.ticket_messages.forEach((message: TicketMessage) => {
                    message.author = userMap.get(message.author_id) || null;
                });
            }

            // Add user information to status history
            if (ticket.ticket_status_history) {
                ticket.ticket_status_history.forEach((history: TicketStatusHistory) => {
                    history.changed_by = userMap.get(history.changed_by) || null;
                });
            }
        }

        return NextResponse.json({ success: true, data: ticket });
    } catch (error) {
        console.error('Ticket fetch error:', error);
        return NextResponse.json({ error: 'Failed to fetch ticket' }, { status: 500 });
    }
});

export const PUT = withAuth(async (userId, request, { params }) => {
    const supabase = createClient();
    const { id } = params;
    const { title, description, priority, assigneeId } = await request.json();

    if (!id) {
        return NextResponse.json({ error: 'Ticket ID is required' }, { status: 400 });
    }

    // Validate title if provided
    if (title !== undefined && (typeof title !== 'string' || title.trim() === '')) {
        return NextResponse.json({ error: 'Title must be a non-empty string' }, { status: 400 });
    }

    // Validate priority if provided
    const validPriorities = ['low', 'medium', 'high', 'urgent'];
    if (priority && !validPriorities.includes(priority)) {
        return NextResponse.json({ error: 'Invalid priority value' }, { status: 400 });
    }

    try {
        // Build update object with only provided fields
        const updateData: any = {
            updated_by: userId
        };

        if (title !== undefined) updateData.title = title.trim();
        if (description !== undefined) updateData.description = description;
        if (priority !== undefined) updateData.priority = priority;
        if (assigneeId !== undefined) updateData.assignee_id = assigneeId;

        const { data: updatedTicket, error } = await supabase
            .from('tickets')
            .update(updateData)
            .eq('id', id)
            .select(`
                *,
                ticket_targets(
                    id,
                    target_type,
                    target_id
                )
            `)
            .single();

        if (error) {
            if (error.code === 'PGRST116') {
                return NextResponse.json({ error: 'Ticket not found or access denied' }, { status: 404 });
            }
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        // Fetch user information separately
        const { data: users, error: usersError } = await supabase.auth.admin.listUsers();

        if (!usersError && users) {
            const userMap = new Map();
            users.users.forEach(user => {
                userMap.set(user.id, { id: user.id, email: user.email });
            });

            // Add user information to ticket
            updatedTicket.creator = userMap.get(updatedTicket.creator_id) || null;
            updatedTicket.assignee = userMap.get(updatedTicket.assignee_id) || null;
        }

        return NextResponse.json({ success: true, data: updatedTicket });
    } catch (error) {
        console.error('Ticket update error:', error);
        return NextResponse.json({ error: 'Failed to update ticket' }, { status: 500 });
    }
});

export const DELETE = withAuth(async (_, request, { params }) => {
    const supabase = createClient();
    const { id } = params;

    if (!id) {
        return NextResponse.json({ error: 'Ticket ID is required' }, { status: 400 });
    }

    try {
        const { error } = await supabase
            .from('tickets')
            .delete()
            .eq('id', id);

        if (error) {
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error('Ticket delete error:', error);
        return NextResponse.json({ error: 'Failed to delete ticket' }, { status: 500 });
    }
});
