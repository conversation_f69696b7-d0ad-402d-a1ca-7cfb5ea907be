import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const POST = withAuth(async (authContext, request) => {
    const { isAdmin } = authContext;
    
    // Only admins can set user roles
    if (!isAdmin) {
        return NextResponse.json({ error: 'Forbidden: Admin access required' }, { status: 403 });
    }

    const supabase = createClient();
    const { userId, role } = await request.json();

    // Validate required fields
    if (!userId || typeof userId !== 'string') {
        return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    if (!role || typeof role !== 'string') {
        return NextResponse.json({ error: 'Role is required' }, { status: 400 });
    }

    // Validate role value
    const validRoles = ['user', 'admin'];
    if (!validRoles.includes(role)) {
        return NextResponse.json({ error: 'Invalid role. Must be one of: user, admin' }, { status: 400 });
    }

    try {
        // Call the database function to set user role
        const { error } = await supabase.rpc('set_user_role', {
            user_id: userId,
            role: role
        });

        if (error) {
            console.error('Error setting user role:', error);
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        return NextResponse.json({ 
            success: true, 
            message: `User role updated to ${role}` 
        });
    } catch (error) {
        console.error('Unexpected error setting user role:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
});
