import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const GET = withAuth(async (authContext, request) => {
    const { isAdmin } = authContext;
    
    // Only admins can list users
    if (!isAdmin) {
        return NextResponse.json({ error: 'Forbidden: Admin access required' }, { status: 403 });
    }

    const supabase = createClient();

    try {
        // Get users from auth.users table
        const { data: users, error } = await supabase
            .from('auth.users')
            .select('id, email, app_metadata, created_at, last_sign_in_at')
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error fetching users:', error);
            return NextResponse.json({ error: error.message }, { status: 500 });
        }

        // Transform the data to include role information
        const transformedUsers = users?.map(user => ({
            id: user.id,
            email: user.email,
            role: user.app_metadata?.role || 'user',
            isAdmin: user.app_metadata?.role === 'admin',
            createdAt: user.created_at,
            lastSignInAt: user.last_sign_in_at
        })) || [];

        return NextResponse.json({ 
            success: true, 
            data: transformedUsers 
        });
    } catch (error) {
        console.error('Unexpected error fetching users:', error);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    }
});
